import React from 'react';
import { BadgeType, EmployeeBadge } from '../types/badge';

interface BadgeImageProps {
  badge: BadgeType | EmployeeBadge;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showTooltip?: boolean;
  daysRemaining?: number;
  awardedDate?: string;
}

const sizeClasses = {
  sm: 'w-10 h-10',
  md: 'w-14 h-14',
  lg: 'w-20 h-20',
  xl: 'w-24 h-24'
};

const BadgeImage: React.FC<BadgeImageProps> = ({
  badge,
  size = 'lg',
  className = '',
  showTooltip = true,
  daysRemaining,
  awardedDate
}) => {
  // Handle both BadgeType and EmployeeBadge
  const badgeType = 'badge_type' in badge ? badge.badge_type : badge as BadgeType;
  const badgeName = badgeType?.name || 'Unknown Badge';
  const badgeColor = badgeType?.color || '#3B82F6';

  // Map badge names to actual image files in public/profiles/badges/
  const getBadgeImagePath = (badgeName: string): string => {
    const badgeImageMap: { [key: string]: string } = {
      'IT Support Professional': '/profiles/badges/it support.jpg',
      'Web Development Professional': '/profiles/badges/full stack.jpg', // Using full stack as fallback
      'Office 365 Professional': '/profiles/badges/office 365.jpg',
      'Full Stack Development': '/profiles/badges/full stack.jpg',
      'Client Acquisition': '/profiles/badges/client acquisition.jpg',
      'Digital Marketing Professional': '/profiles/badges/digital marketing.jpg',
      'Data Analysis Professional': '/profiles/badges/data analytics.jpg',
      'Cloud Deployment Specialist': '/profiles/badges/cloud.jpg'
    };

    return badgeImageMap[badgeName] || '';
  };

  // Fallback to generated badge if image doesn't exist
  const generateBadgeImage = (badgeName: string, color: string): string => {
    const cleanName = badgeName.replace(/Professional|Specialist/g, '').trim();
    const initials = cleanName.split(' ').map(word => word[0]).join('').toUpperCase();
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${color.replace('#', '')}&color=fff&size=128&format=png&bold=true&font-size=0.4`;
  };

  // Try to use database icon_url first, then mapped path, then generated
  const databaseIconUrl = badgeType?.icon_url;
  const mappedImagePath = getBadgeImagePath(badgeName);
  const imagePath = databaseIconUrl || mappedImagePath || generateBadgeImage(badgeName, badgeColor);

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    // If actual image fails, fallback to generated badge
    const target = e.target as HTMLImageElement;
    if (target.src !== generateBadgeImage(badgeName, badgeColor)) {
      target.src = generateBadgeImage(badgeName, badgeColor);
    }
  };

  return (
    <div className={`relative group ${className}`}>
      <div className="relative">
        <img
          src={imagePath}
          alt={badgeName}
          className={`${sizeClasses[size]} rounded-lg border-2 hover:border-opacity-80 transition-all object-cover shadow-sm`}
          style={{ borderColor: badgeColor }}
          onError={handleImageError}
        />
        {daysRemaining !== undefined && daysRemaining < 30 && (
          <span
            className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 border-2 border-white rounded-full"
            title="Expiring soon"
          />
        )}
      </div>
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 whitespace-nowrap z-10 pointer-events-none shadow-lg">
          <div className="font-semibold mb-1">{badgeName}</div>
          {badgeType?.description && (
            <div className="text-gray-300 text-[10px] mb-1">{badgeType.description}</div>
          )}
          {awardedDate && (
            <div className="text-gray-300 text-[10px]">
              Awarded: {new Date(awardedDate).toLocaleDateString()}
            </div>
          )}
          {daysRemaining !== undefined && (
            <div className={`text-[10px] ${daysRemaining < 30 ? 'text-yellow-400' : 'text-gray-300'}`}>
              {daysRemaining} days remaining
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BadgeImage; 
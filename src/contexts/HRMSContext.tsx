import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { supabase } from '../supabaseClient';
import { hrmsService } from '../services/hrmsService';
import {
  Employee,
  LeaveType,
  EmployeeLeaveBalance,
  Notification,
  Holiday
} from '../types/hrms';

interface HRMSContextType {
  // Current user data
  currentEmployee: Employee | null;
  leaveTypes: LeaveType[];
  leaveBalance: EmployeeLeaveBalance[];
  notifications: Notification[];
  holidays: Holiday[];

  // Loading states
  loading: boolean;
  error: string | null;

  // Actions
  refreshEmployeeData: () => Promise<void>;
  refreshLeaveData: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  retryInitialization: () => Promise<void>;

  // Computed values
  unreadNotificationCount: number;
  totalAvailableLeave: number;
}

const HRMSContext = createContext<HRMSContextType | undefined>(undefined);

interface HRMSProviderProps {
  children: ReactNode;
}

export const HRMSProvider: React.FC<HRMSProviderProps> = ({ children }) => {
  const [currentEmployee, setCurrentEmployee] = useState<Employee | null>(null);
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([]);
  const [leaveBalance, setLeaveBalance] = useState<EmployeeLeaveBalance[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Refs for cleanup and timeout management
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const isVisibleRef = useRef(true);

  // Tab visibility handling (moved after function definitions)
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden;

      if (!document.hidden && error) {
        // Tab became visible and there was an error, retry
        console.log('🔄 Tab became visible, retrying HRMS initialization...');
        initializeHRMSData(); // Call directly to avoid dependency issues
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [error]);

  useEffect(() => {
    initializeHRMSData();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await initializeHRMSData();
        } else if (event === 'SIGNED_OUT') {
          // Clear all data when user signs out
          cleanup();
          setCurrentEmployee(null);
          setLeaveBalance([]);
          setNotifications([]);
          setHolidays([]);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
      cleanup();
    };
  }, []);

  const cleanup = () => {
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
      initTimeoutRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  };

  const initializeHRMSData = async () => {
    try {
      console.log('🚀 Initializing HRMS data...');
      setLoading(true);
      setError(null);
      cleanup(); // Clean up any previous operations

      // Create new abort controller for this initialization
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      // Set timeout for initialization
      initTimeoutRef.current = setTimeout(() => {
        console.warn('⏰ HRMS initialization timeout');
        setError('Initialization timeout. Please refresh the page.');
        setLoading(false);
        cleanup();
      }, 30000); // 30 second timeout

      // Check if user is authenticated
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('❌ No user authenticated, skipping HRMS initialization');
        setLoading(false);
        cleanup();
        return;
      }

      if (signal.aborted) return;
      console.log('👤 User authenticated:', user.id);

      // Load data with timeout and abort signal
      const loadWithTimeout = async (loadFn: () => Promise<void>, name: string) => {
        try {
          if (signal.aborted) return;
          await Promise.race([
            loadFn(),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error(`${name} timeout`)), 10000)
            )
          ]);
          console.log(`✅ ${name} loaded`);
        } catch (error) {
          console.error(`❌ Error loading ${name}:`, error);
          if (!signal.aborted) {
            throw error; // Re-throw if not aborted
          }
        }
      };

      // Load data sequentially with individual timeouts
      await loadWithTimeout(refreshEmployeeData, 'Employee data');
      if (signal.aborted) return;

      await loadWithTimeout(loadLeaveTypes, 'Leave types');
      if (signal.aborted) return;

      await loadWithTimeout(loadHolidays, 'Holidays');
      if (signal.aborted) return;

    } catch (error) {
      console.error('❌ Error initializing HRMS data:', error);
      if (!abortControllerRef.current?.signal.aborted) {
        setError(error instanceof Error ? error.message : 'Failed to load HRMS data');
      }
    } finally {
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
        initTimeoutRef.current = null;
      }
      console.log('🏁 HRMS initialization complete');
      setLoading(false);
    }
  };

  const retryInitialization = async () => {
    console.log('🔄 Retrying HRMS initialization...');
    await initializeHRMSData();
  };

  const refreshEmployeeData = async () => {
    try {
      console.log('🔄 Refreshing employee data...');
      const employee = await hrmsService.getCurrentEmployee();
      console.log('👤 Employee data received:', employee);
      setCurrentEmployee(employee);

      if (employee) {
        console.log('📊 Loading employee-specific data...');
        // Load employee-specific data
        try {
          await refreshLeaveData();
          console.log('✅ Leave data loaded');
        } catch (error) {
          console.error('❌ Error loading leave data:', error);
        }

        try {
          await refreshNotifications();
          console.log('✅ Notifications loaded');
        } catch (error) {
          console.error('❌ Error loading notifications:', error);
        }
      } else {
        console.log('⚠️ No employee data found');
      }
    } catch (error) {
      console.error('❌ Error refreshing employee data:', error);
      // Set employee to null on error to prevent infinite loading
      setCurrentEmployee(null);
    }
  };

  const loadLeaveTypes = async () => {
    try {
      const types = await hrmsService.getLeaveTypes();
      setLeaveTypes(types);
    } catch (error) {
      console.error('Error loading leave types:', error);
    }
  };

  const refreshLeaveData = async () => {
    if (!currentEmployee) return;
    
    try {
      const balance = await hrmsService.getLeaveBalance(currentEmployee.id);
      setLeaveBalance(balance);
    } catch (error) {
      console.error('Error refreshing leave data:', error);
    }
  };

  const refreshNotifications = async () => {
    if (!currentEmployee) return;
    
    try {
      const notifs = await hrmsService.getNotifications(currentEmployee.id);
      setNotifications(notifs);
    } catch (error) {
      console.error('Error refreshing notifications:', error);
    }
  };

  const loadHolidays = async () => {
    try {
      const currentYear = new Date().getFullYear();
      const holidayList = await hrmsService.getHolidays(currentYear);
      setHolidays(holidayList);
    } catch (error) {
      console.error('Error loading holidays:', error);
    }
  };

  const markNotificationAsRead = async (notificationId: string) => {
    try {
      await hrmsService.markNotificationAsRead(notificationId);
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, read_status: true }
            : notif
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Note: Network retry functionality can be added later if needed

  // Computed values
  const unreadNotificationCount = notifications.filter(n => !n.read_status).length;
  const totalAvailableLeave = leaveBalance.reduce((sum, balance) => sum + balance.available_days, 0);

  const contextValue: HRMSContextType = {
    currentEmployee,
    leaveTypes,
    leaveBalance,
    notifications,
    holidays,
    loading,
    error,
    refreshEmployeeData,
    refreshLeaveData,
    refreshNotifications,
    markNotificationAsRead,
    retryInitialization,
    unreadNotificationCount,
    totalAvailableLeave
  };

  return (
    <HRMSContext.Provider value={contextValue}>
      {children}
    </HRMSContext.Provider>
  );
};

export const useHRMS = (): HRMSContextType => {
  const context = useContext(HRMSContext);
  if (context === undefined) {
    throw new Error('useHRMS must be used within an HRMSProvider');
  }
  return context;
};

// Hook for checking user permissions
export const useHRMSPermissions = () => {
  const { currentEmployee } = useHRMS();
  
  const isManager = currentEmployee?.designation?.toLowerCase().includes('manager') || false;
  const isHR = currentEmployee?.department?.toLowerCase().includes('hr') || 
               currentEmployee?.department?.toLowerCase().includes('human') || false;
  const isAdmin = isHR; // For now, HR is considered admin
  
  const canApproveLeaves = isManager || isHR;
  const canViewAllEmployees = isHR;
  const canManagePayroll = isHR;
  const canViewReports = isManager || isHR;
  const canManageEmployees = isHR;
  
  return {
    isManager,
    isHR,
    isAdmin,
    canApproveLeaves,
    canViewAllEmployees,
    canManagePayroll,
    canViewReports,
    canManageEmployees
  };
};

// Hook for dashboard data
export const useHRMSDashboard = () => {
  const { currentEmployee, leaveBalance, notifications } = useHRMS();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentEmployee) {
      loadDashboardData();
    }
  }, [currentEmployee]);

  const loadDashboardData = async () => {
    if (!currentEmployee) return;
    
    try {
      setLoading(true);
      const data = await hrmsService.getDashboardStats(currentEmployee.id);
      setDashboardData(data);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  return {
    dashboardData,
    loading,
    refreshDashboard: loadDashboardData
  };
};



// Hook for leave management
export const useLeaveManagement = () => {
  const { currentEmployee, leaveBalance, refreshLeaveData } = useHRMS();
  const [submitting, setSubmitting] = useState(false);

  const applyLeave = async (leaveData: any) => {
    if (!currentEmployee) return;
    
    try {
      setSubmitting(true);
      await hrmsService.applyLeave({
        ...leaveData,
        employee_id: currentEmployee.id
      });
      await refreshLeaveData(); // Refresh leave data
    } catch (error) {
      console.error('Error applying for leave:', error);
      throw error;
    } finally {
      setSubmitting(false);
    }
  };

  const getAvailableLeave = (leaveTypeId: string): number => {
    const balance = leaveBalance.find(lb => lb.leave_type_id === leaveTypeId);
    return balance?.available_days || 0;
  };

  return {
    applyLeave,
    getAvailableLeave,
    submitting,
    leaveBalance
  };
};

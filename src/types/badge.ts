export interface BadgeType {
  id: string;
  name: string;
  description: string;
  color?: string;
  icon_url?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface EmployeeBadge {
  id: string;
  employee_id: string;
  badge_id: string;
  assigned_by: string;
  assigned_date: string;
  expiry_date?: string;
  status: 'active' | 'expired' | 'revoked';
  revoked_by?: string;
  revoked_date?: string;
  revocation_reason?: string;
  notes?: string;
  achievement_data?: any;
  created_at: string;
  updated_at: string;
  // Joined data
  badge_type?: BadgeType;
  assigned_by_employee?: {
    first_name: string;
    last_name: string;
  };
}

export interface BadgeAssignment {
  employee_id: string;
  badge_type_id: string;
  notes?: string;
  expiry_date?: string;
}

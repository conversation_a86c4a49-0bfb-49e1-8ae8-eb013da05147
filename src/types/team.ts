export interface Badge {
  id: string;
  name: string;
  description: string;
  awarded_date?: string;
  expiry_days?: number;
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  team?: string;
  badges?: Badge[];
  isLeadership?: boolean;
  designation?: string;
  employeeCode?: string;
  status?: 'active' | 'inactive' | 'on_leave' | 'terminated';
  uuid?: string;
}

export interface TeamData {
  name: string;
  ceo?: TeamMember;
  cro?: TeamMember;
  sdm?: TeamMember;
  tdm?: TeamMember;
  cxm?: TeamMember;
  members: TeamMember[];
}

export interface Teams {
  [key: string]: TeamData;
}